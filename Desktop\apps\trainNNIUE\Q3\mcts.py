# mcts.py
import torch
import chess
from model import ChessNet
from utils import board_to_planes_fast, MOVE_TO_IDX
import numpy as np

class Node:
    def __init__(self, board, prior=0):
        self.board = board.copy()
        self.prior = prior
        self.children = {}
        self.visits = 0
        self.value_sum = 0

    def value(self):
        return 0 if self.visits == 0 else self.value_sum / self.visits

def infer(model, board):
    model.eval()
    with torch.no_grad():
        x = torch.tensor(board_to_planes_fast(board)).unsqueeze(0)
        policy_logits, value = model(x)
        policy = torch.softmax(policy_logits, dim=-1).squeeze().numpy()
    return policy, value.item()

def mcts_search(model, board, num_sim=100, cpuct=1.0):
    root = Node(board)

    for _ in range(num_sim):
        node = root
        path = [node]

        # Selection
        while node.children:
            children = list(node.children.values())
            uct_scores = [
                child.value() + cpuct * child.prior * np.sqrt(node.visits) / (1 + child.visits)
                for child in children
            ]
            node = children[np.argmax(uct_scores)]
            path.append(node)

        # Expansion
        if not node.board.is_game_over():
            policy, value = infer(model, node.board)
            for move in node.board.legal_moves:
                uci = move.uci()
                prior = policy[MOVE_TO_IDX[uci]] if uci in MOVE_TO_IDX else 0
                child_node = Node(node.board, prior=prior)
                child_node.board.push(move)
                node.children[move] = child_node
        else:
            result = node.board.result()
            value = 1.0 if result == "1-0" else 0.0 if result == "0-1" else 0.5

        # Backpropagation
        for node in path:
            node.visits += 1
            node.value_sum += value if node.board.turn == chess.WHITE else 1 - value

    # Choose most visited move
    best_move = max(root.children.keys(), key=lambda m: root.children[m].visits)
    return best_move