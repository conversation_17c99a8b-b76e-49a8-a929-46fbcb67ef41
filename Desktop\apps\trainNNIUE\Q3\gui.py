# gui.py
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
import threading
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import os
import json
from datetime import datetime
from train import train_model
from dataloader import ChessData<PERSON>, collate_fn
from torch.utils.data import DataLoader

class ChessTrainerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("♟️ Chess AI Trainer")
        self.train_loader = None
        self.val_loader = None
        self.resume_path = None

        # JSON persistence files
        self.settings_file = "gui_settings.json"
        self.processed_files_db = "processed_files.json"

        # Batch training variables
        self.pgn_folder = None
        self.processed_pgns = set()
        self.batch_training_active = False

        # Load settings and processed files
        self.load_settings()
        self.load_processed_files()

        self.setup_ui()

        # Apply saved window settings
        self.apply_window_settings()

        # Bind window events to save settings
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_ui(self):
        row = 0
        tk.Button(self.root, text="📂 Load Training Data", command=self.load_train).grid(row=row, column=0, pady=5, padx=5)
        self.train_label = tk.Label(self.root, text="❌ Not loaded")
        self.train_label.grid(row=row, column=1, sticky='w', padx=5)

        row += 1
        tk.Button(self.root, text="📂 Load Validation Data", command=self.load_val).grid(row=row, column=0, pady=5, padx=5)
        self.val_label = tk.Label(self.root, text="❌ Not loaded")
        self.val_label.grid(row=row, column=1, sticky='w', padx=5)

        # Puzzle mode toggle
        row += 1
        self.puzzle_mode = tk.BooleanVar(value=False)
        puzzle_frame = tk.Frame(self.root)
        puzzle_frame.grid(row=row, column=0, columnspan=2, sticky='ew', pady=5)

        tk.Checkbutton(puzzle_frame, text="🧩 Puzzle Mode", variable=self.puzzle_mode,
                      command=self.toggle_puzzle_mode).pack(side=tk.LEFT)
        tk.Label(puzzle_frame, text="(Auto-split PGN into train/val)").pack(side=tk.LEFT, padx=5)

        # Validation split percentage
        row += 1
        self.split_frame = tk.Frame(self.root)
        self.split_frame.grid(row=row, column=0, columnspan=2, sticky='ew', pady=2)

        tk.Label(self.split_frame, text="📊 Validation Split %:").pack(side=tk.LEFT)
        self.val_split_entry = tk.Entry(self.split_frame, width=10)
        self.val_split_entry.insert(0, "20")
        self.val_split_entry.pack(side=tk.LEFT, padx=5)
        tk.Label(self.split_frame, text="(% of data for validation)").pack(side=tk.LEFT)

        # Initially hide split controls
        self.split_frame.grid_remove()

        row += 1
        tk.Button(self.root, text="🔁 Resume from Checkpoint", command=self.load_checkpoint).grid(row=row, column=0, pady=5, padx=5)
        self.ckpt_label = tk.Label(self.root, text="❌ None")
        self.ckpt_label.grid(row=row, column=1, sticky='w', padx=5)

        row += 1
        tk.Label(self.root, text="🎯 Max Train Games:").grid(row=row, column=0, sticky='e')
        self.max_train_games_entry = tk.Entry(self.root, width=10)
        self.max_train_games_entry.insert(0, "ALL")
        self.max_train_games_entry.grid(row=row, column=1, sticky='w', padx=5)

        row += 1
        tk.Label(self.root, text="🎯 Max Val Games:").grid(row=row, column=0, sticky='e')
        self.max_val_games_entry = tk.Entry(self.root, width=10)
        self.max_val_games_entry.insert(0, "ALL")
        self.max_val_games_entry.grid(row=row, column=1, sticky='w', padx=5)

        row += 1
        tk.Label(self.root, text="📊 Epochs:").grid(row=row, column=0, sticky='e')
        self.epochs_entry = tk.Entry(self.root, width=10)
        self.epochs_entry.insert(0, "10")
        self.epochs_entry.grid(row=row, column=1, sticky='w', padx=5)

        row += 1
        self.start_btn = tk.Button(self.root, text="▶️ Start Training", command=self.start_training, bg="lightgreen")
        self.start_btn.grid(row=row, column=0, columnspan=2, pady=10)

        # Batch training section
        row += 1
        tk.Label(self.root, text="🗂️ Batch Training:", font=("Arial", 10, "bold")).grid(row=row, column=0, columnspan=2, sticky='w', pady=(10,0))

        row += 1
        tk.Button(self.root, text="📁 Select PGN Folder", command=self.select_pgn_folder, bg="lightblue").grid(row=row, column=0, pady=5, padx=5)
        self.folder_label = tk.Label(self.root, text="❌ No folder selected")
        self.folder_label.grid(row=row, column=1, sticky='w', padx=5)

        row += 1
        tk.Label(self.root, text="📊 Epochs per PGN:").grid(row=row, column=0, sticky='e')
        self.epochs_per_pgn_entry = tk.Entry(self.root, width=10)
        self.epochs_per_pgn_entry.insert(0, "5")
        self.epochs_per_pgn_entry.grid(row=row, column=1, sticky='w', padx=5)

        row += 1
        self.batch_start_btn = tk.Button(self.root, text="🚀 Start Batch Training", command=self.start_batch_training, bg="orange")
        self.batch_start_btn.grid(row=row, column=0, pady=10, sticky='ew')
        self.batch_stop_btn = tk.Button(self.root, text="⏹️ Stop Batch", command=self.stop_batch_training, bg="lightcoral")
        self.batch_stop_btn.grid(row=row, column=1, pady=10, sticky='ew')

        row += 1
        self.batch_status_label = tk.Label(self.root, text="", fg="blue", wraplength=400)
        self.batch_status_label.grid(row=row, column=0, columnspan=2, sticky='w')

        # Processed files management
        row += 1
        tk.Button(self.root, text="📋 View Processed Files", command=self.view_processed_files, bg="lightgray").grid(row=row, column=0, pady=5, sticky='ew')
        tk.Button(self.root, text="🗑️ Clear Processed List", command=self.clear_processed_files, bg="lightcoral").grid(row=row, column=1, pady=5, sticky='ew')

        # Plot
        row += 1
        self.figure, (self.ax1, self.ax2) = plt.subplots(1, 2, figsize=(12, 4))
        self.canvas = FigureCanvasTkAgg(self.figure, self.root)
        self.canvas.get_tk_widget().grid(row=row, column=0, columnspan=2, pady=10)

        # Initialize plot data
        self.train_losses = {'policy': [], 'value': [], 'acc': []}
        self.val_losses = {'policy': [], 'value': [], 'acc': []}
        self.epochs_data = []

        self.setup_plots()

    def load_settings(self):
        """Load GUI settings from JSON file"""
        self.default_settings = {
            "window_geometry": "800x900+100+100",
            "max_train_games": "ALL",
            "max_val_games": "ALL",
            "epochs": "10",
            "epochs_per_pgn": "5",
            "val_split_percent": "20",
            "puzzle_mode": False,
            "last_train_path": "",
            "last_val_path": "",
            "last_checkpoint_path": "",
            "last_pgn_folder": ""
        }

        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r') as f:
                    self.settings = json.load(f)
                # Merge with defaults for any missing keys
                for key, value in self.default_settings.items():
                    if key not in self.settings:
                        self.settings[key] = value
            else:
                self.settings = self.default_settings.copy()
        except Exception as e:
            print(f"Error loading settings: {e}")
            self.settings = self.default_settings.copy()

    def save_settings(self):
        """Save current GUI settings to JSON file"""
        try:
            # Update settings with current values
            self.settings["window_geometry"] = self.root.geometry()
            self.settings["max_train_games"] = self.max_train_games_entry.get()
            self.settings["max_val_games"] = self.max_val_games_entry.get()
            self.settings["epochs"] = self.epochs_entry.get()
            self.settings["epochs_per_pgn"] = self.epochs_per_pgn_entry.get()
            self.settings["val_split_percent"] = self.val_split_entry.get()
            self.settings["puzzle_mode"] = self.puzzle_mode.get()

            with open(self.settings_file, 'w') as f:
                json.dump(self.settings, f, indent=2)
        except Exception as e:
            print(f"Error saving settings: {e}")

    def load_processed_files(self):
        """Load processed files database from JSON"""
        try:
            if os.path.exists(self.processed_files_db):
                with open(self.processed_files_db, 'r') as f:
                    data = json.load(f)
                    self.processed_pgns = set(data.get("processed_files", []))
                    self.file_metadata = data.get("file_metadata", {})
            else:
                self.processed_pgns = set()
                self.file_metadata = {}

            # Migrate from old text file if it exists
            old_file = "processed_pgns.txt"
            if os.path.exists(old_file) and not os.path.exists(self.processed_files_db):
                with open(old_file, 'r') as f:
                    old_files = set(line.strip() for line in f)
                    self.processed_pgns.update(old_files)
                    # Add basic metadata for migrated files
                    for filename in old_files:
                        self.file_metadata[filename] = {
                            "processed_date": "migrated",
                            "epochs_trained": "unknown",
                            "file_size": "unknown"
                        }
                self.save_processed_files()
                print(f"Migrated {len(old_files)} files from old format")

        except Exception as e:
            print(f"Error loading processed files: {e}")
            self.processed_pgns = set()
            self.file_metadata = {}

    def save_processed_files(self):
        """Save processed files database to JSON"""
        try:
            data = {
                "processed_files": list(self.processed_pgns),
                "file_metadata": self.file_metadata,
                "last_updated": datetime.now().isoformat()
            }
            with open(self.processed_files_db, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            print(f"Error saving processed files: {e}")

    def apply_window_settings(self):
        """Apply saved window settings"""
        try:
            # Set window geometry
            self.root.geometry(self.settings["window_geometry"])

            # Set form values
            self.max_train_games_entry.delete(0, tk.END)
            self.max_train_games_entry.insert(0, self.settings["max_train_games"])

            self.max_val_games_entry.delete(0, tk.END)
            self.max_val_games_entry.insert(0, self.settings["max_val_games"])

            self.epochs_entry.delete(0, tk.END)
            self.epochs_entry.insert(0, self.settings["epochs"])

            self.epochs_per_pgn_entry.delete(0, tk.END)
            self.epochs_per_pgn_entry.insert(0, self.settings["epochs_per_pgn"])

            self.val_split_entry.delete(0, tk.END)
            self.val_split_entry.insert(0, self.settings["val_split_percent"])

            self.puzzle_mode.set(self.settings["puzzle_mode"])
            self.toggle_puzzle_mode()  # Apply puzzle mode state

        except Exception as e:
            print(f"Error applying window settings: {e}")

    def on_closing(self):
        """Handle window closing event"""
        self.save_settings()
        self.root.destroy()

    def toggle_puzzle_mode(self):
        """Toggle puzzle mode and show/hide relevant controls"""
        if self.puzzle_mode.get():
            # Show split controls, hide validation load button
            self.split_frame.grid()
            self.val_label.config(text="🧩 Auto-split mode enabled")
        else:
            # Hide split controls, show validation load button
            self.split_frame.grid_remove()
            if not self.val_loader:
                self.val_label.config(text="❌ Not loaded")

    def setup_plots(self):
        """Initialize the loss plots"""
        # Clear plots
        self.ax1.clear()
        self.ax2.clear()

        # Loss plot
        self.ax1.set_title('Training & Validation Loss')
        self.ax1.set_xlabel('Epoch')
        self.ax1.set_ylabel('Loss')
        self.ax1.grid(True, alpha=0.3)

        # Accuracy plot
        self.ax2.set_title('Training & Validation Accuracy')
        self.ax2.set_xlabel('Epoch')
        self.ax2.set_ylabel('Accuracy (%)')
        self.ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        self.canvas.draw()

    def update_plots(self, epoch, train_policy_loss, train_value_loss, train_acc,
                     val_policy_loss, val_value_loss, val_acc):
        """Update the plots with new data"""
        # Store data
        self.epochs_data.append(epoch)
        self.train_losses['policy'].append(train_policy_loss)
        self.train_losses['value'].append(train_value_loss)
        self.train_losses['acc'].append(train_acc)
        self.val_losses['policy'].append(val_policy_loss)
        self.val_losses['value'].append(val_value_loss)
        self.val_losses['acc'].append(val_acc)

        # Clear and redraw plots
        self.ax1.clear()
        self.ax2.clear()

        # Loss plot
        self.ax1.plot(self.epochs_data, self.train_losses['policy'], 'b-', label='Train Policy Loss', linewidth=2)
        self.ax1.plot(self.epochs_data, self.train_losses['value'], 'b--', label='Train Value Loss', linewidth=2)
        self.ax1.plot(self.epochs_data, self.val_losses['policy'], 'r-', label='Val Policy Loss', linewidth=2)
        self.ax1.plot(self.epochs_data, self.val_losses['value'], 'r--', label='Val Value Loss', linewidth=2)
        self.ax1.set_title('Training & Validation Loss')
        self.ax1.set_xlabel('Epoch')
        self.ax1.set_ylabel('Loss')
        self.ax1.grid(True, alpha=0.3)
        self.ax1.legend()

        # Accuracy plot
        self.ax2.plot(self.epochs_data, self.train_losses['acc'], 'b-', label='Train Accuracy', linewidth=2)
        self.ax2.plot(self.epochs_data, self.val_losses['acc'], 'r-', label='Val Accuracy', linewidth=2)
        self.ax2.set_title('Training & Validation Accuracy')
        self.ax2.set_xlabel('Epoch')
        self.ax2.set_ylabel('Accuracy (%)')
        self.ax2.grid(True, alpha=0.3)
        self.ax2.legend()

        plt.tight_layout()
        self.canvas.draw()

    def load_train(self):
        initial_dir = os.path.dirname(self.settings["last_train_path"]) if self.settings["last_train_path"] else ""
        path = filedialog.askopenfilename(
            title="Select Training PGN",
            filetypes=[("PGN Files", "*.pgn")],
            initialdir=initial_dir
        )
        if path:
            max_games_str = self.max_train_games_entry.get().strip().upper()
            max_games = None if max_games_str == "ALL" else int(max_games_str) if max_games_str.isdigit() else 500

            self.train_dataset = ChessDataset(path, max_games=max_games, augment=True)
            self.train_loader = DataLoader(self.train_dataset, batch_size=64, shuffle=True, num_workers=0, collate_fn=collate_fn)
            games_text = "ALL" if max_games is None else str(max_games)
            self.train_label.config(text=f"✅ {os.path.basename(path)[:20]} ({games_text} games)")

            # Remember this path
            self.settings["last_train_path"] = path

    def load_val(self):
        initial_dir = os.path.dirname(self.settings["last_val_path"]) if self.settings["last_val_path"] else ""
        path = filedialog.askopenfilename(
            title="Select Validation PGN",
            filetypes=[("PGN Files", "*.pgn")],
            initialdir=initial_dir
        )
        if path:
            max_games_str = self.max_val_games_entry.get().strip().upper()
            max_games = None if max_games_str == "ALL" else int(max_games_str) if max_games_str.isdigit() else 100

            self.val_dataset = ChessDataset(path, max_games=max_games, augment=False)
            self.val_loader = DataLoader(self.val_dataset, batch_size=64, shuffle=False, num_workers=0, collate_fn=collate_fn)
            games_text = "ALL" if max_games is None else str(max_games)
            self.val_label.config(text=f"✅ {os.path.basename(path)[:20]} ({games_text} games)")

            # Remember this path
            self.settings["last_val_path"] = path

    def load_checkpoint(self):
        initial_dir = os.path.dirname(self.settings["last_checkpoint_path"]) if self.settings["last_checkpoint_path"] else ""
        path = filedialog.askopenfilename(
            title="Select Checkpoint",
            filetypes=[("PT Files", "*.pt")],
            initialdir=initial_dir
        )
        if path:
            self.resume_path = path
            self.ckpt_label.config(text="✅ " + os.path.basename(path))

            # Remember this path
            self.settings["last_checkpoint_path"] = path

    def start_training(self):
        if self.puzzle_mode.get():
            # In puzzle mode, only need training data
            if not self.train_loader:
                messagebox.showerror("Error", "Please load training data!")
                return
        else:
            # In normal mode, need both training and validation data
            if not self.train_loader or not self.val_loader:
                messagebox.showerror("Error", "Please load both training and validation data!")
                return

        epochs = self.epochs_entry.get()
        if not epochs.isdigit():
            messagebox.showerror("Error", "Enter valid number of epochs!")
            return

        self.epochs = int(epochs)
        # Clear previous plot data
        self.train_losses = {'policy': [], 'value': [], 'acc': []}
        self.val_losses = {'policy': [], 'value': [], 'acc': []}
        self.epochs_data = []
        self.setup_plots()

        threading.Thread(target=self.run_training, daemon=True).start()

    def run_training(self):
        try:
            if self.puzzle_mode.get():
                # In puzzle mode, split the training dataset
                try:
                    val_split = float(self.val_split_entry.get())
                except:
                    val_split = 20.0  # Default 20%

                # Get the original dataset from train_loader
                original_dataset = self.train_loader.dataset
                train_subset, val_subset = self.split_dataset(original_dataset, val_split)

                # Create new loaders with split data
                train_loader = DataLoader(train_subset, batch_size=64, shuffle=True, num_workers=0, collate_fn=collate_fn)
                val_loader = DataLoader(val_subset, batch_size=64, shuffle=False, num_workers=0, collate_fn=collate_fn)
            else:
                # Use existing loaders
                train_loader = self.train_loader
                val_loader = self.val_loader

            self.train_model_with_gui(train_loader, val_loader,
                                    resume_path=self.resume_path, num_epochs=self.epochs)
            self.root.after(0, lambda: messagebox.showinfo("Success", "🎉 Training completed!"))
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Error", f"❌ Training failed:\n{str(e)}"))

    def select_pgn_folder(self):
        """Select folder containing PGN files for batch training"""
        from tkinter import filedialog
        initial_dir = self.settings["last_pgn_folder"] if self.settings["last_pgn_folder"] else ""
        folder = filedialog.askdirectory(
            title="Select folder containing PGN files",
            initialdir=initial_dir
        )
        if folder:
            self.pgn_folder = folder
            pgn_files = [f for f in os.listdir(folder) if f.endswith('.pgn')]
            processed_count = len([f for f in pgn_files if f in self.processed_pgns])
            remaining_count = len(pgn_files) - processed_count

            self.folder_label.config(text=f"✅ {len(pgn_files)} PGN files ({remaining_count} unprocessed)")

            # Remember this folder
            self.settings["last_pgn_folder"] = folder

    def split_dataset(self, dataset, val_split_percent):
        """Split a dataset into training and validation parts"""
        import random

        # Get all indices
        total_size = len(dataset)
        indices = list(range(total_size))

        # Shuffle indices for random split
        random.shuffle(indices)

        # Calculate split point
        val_size = int(total_size * val_split_percent / 100)
        train_size = total_size - val_size

        # Split indices
        train_indices = indices[:train_size]
        val_indices = indices[train_size:]

        # Create subset datasets
        from torch.utils.data import Subset
        train_subset = Subset(dataset, train_indices)
        val_subset = Subset(dataset, val_indices)

        print(f"Dataset split: {train_size} training, {val_size} validation ({val_split_percent}%)")
        return train_subset, val_subset

    def save_processed_pgn(self, pgn_filename, epochs_trained=None, file_path=None):
        """Mark a PGN file as processed with metadata"""
        self.processed_pgns.add(pgn_filename)

        # Add metadata
        file_size = "unknown"
        if file_path and os.path.exists(file_path):
            try:
                file_size = os.path.getsize(file_path)
            except:
                file_size = "unknown"

        self.file_metadata[pgn_filename] = {
            "processed_date": datetime.now().isoformat(),
            "epochs_trained": epochs_trained or "unknown",
            "file_size": file_size,
            "file_path": file_path or "unknown"
        }

        # Save to JSON
        self.save_processed_files()
        print(f"✅ Marked {pgn_filename} as processed")

    def view_processed_files(self):
        """Show a window with processed files and their metadata"""
        if not self.processed_pgns:
            messagebox.showinfo("Processed Files", "No files have been processed yet.")
            return

        # Create new window
        window = tk.Toplevel(self.root)
        window.title("📋 Processed Files")
        window.geometry("800x600")

        # Create frame with scrollbar
        main_frame = tk.Frame(window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Scrollable text widget
        text_frame = tk.Frame(main_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)

        scrollbar = tk.Scrollbar(text_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        text_widget = tk.Text(text_frame, yscrollcommand=scrollbar.set, wrap=tk.WORD)
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=text_widget.yview)

        # Populate with processed files info
        content = f"📋 Processed Files ({len(self.processed_pgns)} total)\n"
        content += "=" * 60 + "\n\n"

        for filename in sorted(self.processed_pgns):
            content += f"📄 {filename}\n"
            if filename in self.file_metadata:
                metadata = self.file_metadata[filename]
                content += f"   📅 Processed: {metadata.get('processed_date', 'unknown')}\n"
                content += f"   🎯 Epochs: {metadata.get('epochs_trained', 'unknown')}\n"
                content += f"   📊 Size: {metadata.get('file_size', 'unknown')} bytes\n"
                content += f"   📁 Path: {metadata.get('file_path', 'unknown')}\n"
            content += "\n"

        text_widget.insert(tk.END, content)
        text_widget.config(state=tk.DISABLED)

        # Buttons
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        tk.Button(button_frame, text="🔄 Refresh",
                 command=lambda: self.refresh_processed_view(text_widget)).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="❌ Close",
                 command=window.destroy).pack(side=tk.RIGHT, padx=5)

    def refresh_processed_view(self, text_widget):
        """Refresh the processed files view"""
        text_widget.config(state=tk.NORMAL)
        text_widget.delete(1.0, tk.END)

        content = f"📋 Processed Files ({len(self.processed_pgns)} total)\n"
        content += "=" * 60 + "\n\n"

        for filename in sorted(self.processed_pgns):
            content += f"📄 {filename}\n"
            if filename in self.file_metadata:
                metadata = self.file_metadata[filename]
                content += f"   📅 Processed: {metadata.get('processed_date', 'unknown')}\n"
                content += f"   🎯 Epochs: {metadata.get('epochs_trained', 'unknown')}\n"
                content += f"   📊 Size: {metadata.get('file_size', 'unknown')} bytes\n"
                content += f"   📁 Path: {metadata.get('file_path', 'unknown')}\n"
            content += "\n"

        text_widget.insert(tk.END, content)
        text_widget.config(state=tk.DISABLED)

    def clear_processed_files(self):
        """Clear the processed files list after confirmation"""
        if not self.processed_pgns:
            messagebox.showinfo("Clear Processed Files", "No processed files to clear.")
            return

        result = messagebox.askyesno(
            "Clear Processed Files",
            f"Are you sure you want to clear {len(self.processed_pgns)} processed files?\n\n"
            "This will allow them to be processed again in batch training."
        )

        if result:
            self.processed_pgns.clear()
            self.file_metadata.clear()
            self.save_processed_files()
            messagebox.showinfo("Success", "✅ Processed files list cleared!")

            # Update folder label if folder is selected
            if self.pgn_folder:
                pgn_files = [f for f in os.listdir(self.pgn_folder) if f.endswith('.pgn')]
                self.folder_label.config(text=f"✅ {len(pgn_files)} PGN files ({len(pgn_files)} unprocessed)")

    def get_next_pgn_file(self):
        """Get the next unprocessed PGN file from the folder"""
        if not self.pgn_folder:
            return None

        pgn_files = [f for f in os.listdir(self.pgn_folder) if f.endswith('.pgn')]
        unprocessed_files = [f for f in pgn_files if f not in self.processed_pgns]

        return unprocessed_files[0] if unprocessed_files else None

    def start_batch_training(self):
        """Start batch training on multiple PGN files"""
        if not self.pgn_folder:
            messagebox.showerror("Error", "Please select a PGN folder first!")
            return

        if not self.puzzle_mode.get() and not self.val_loader:
            messagebox.showerror("Error", "Please load validation data first or enable Puzzle Mode!")
            return

        epochs_per_pgn = self.epochs_per_pgn_entry.get()
        if not epochs_per_pgn.isdigit():
            messagebox.showerror("Error", "Enter valid number of epochs per PGN!")
            return

        self.batch_training_active = True
        self.epochs_per_pgn = int(epochs_per_pgn)

        # Clear previous plot data
        self.train_losses = {'policy': [], 'value': [], 'acc': []}
        self.val_losses = {'policy': [], 'value': [], 'acc': []}
        self.epochs_data = []
        self.setup_plots()

        threading.Thread(target=self.run_batch_training, daemon=True).start()

    def stop_batch_training(self):
        """Stop batch training"""
        self.batch_training_active = False
        self.batch_status_label.config(text="⏹️ Batch training stopped by user.")

    def run_batch_training(self):
        """Run batch training across multiple PGN files"""
        try:
            total_pgns = len([f for f in os.listdir(self.pgn_folder) if f.endswith('.pgn')])
            processed_count = 0

            while self.batch_training_active:
                next_pgn = self.get_next_pgn_file()
                if not next_pgn:
                    self.root.after(0, lambda: self.batch_status_label.config(
                        text="🎉 Batch training completed! All PGN files processed."))
                    break

                processed_count += 1
                pgn_path = os.path.join(self.pgn_folder, next_pgn)

                # Update status
                self.root.after(0, lambda p=next_pgn, c=processed_count, t=total_pgns:
                               self.batch_status_label.config(
                                   text=f"📚 Processing {p} ({c}/{t})..."))

                # Load training data for this PGN
                max_games_str = self.max_train_games_entry.get().strip().upper()
                max_games = None if max_games_str == "ALL" else int(max_games_str) if max_games_str.isdigit() else 500

                full_dataset = ChessDataset(pgn_path, max_games=max_games, augment=True)
                print(f"Loaded {len(full_dataset)} positions from {next_pgn}")

                if self.puzzle_mode.get():
                    # Split dataset into train/val
                    try:
                        val_split = float(self.val_split_entry.get())
                    except:
                        val_split = 20.0  # Default 20%

                    train_subset, val_subset = self.split_dataset(full_dataset, val_split)
                    train_loader = DataLoader(train_subset, batch_size=64, shuffle=True, num_workers=0, collate_fn=collate_fn)
                    val_loader = DataLoader(val_subset, batch_size=64, shuffle=False, num_workers=0, collate_fn=collate_fn)
                else:
                    # Use full dataset for training, existing val_loader for validation
                    train_loader = DataLoader(full_dataset, batch_size=64, shuffle=True, num_workers=0, collate_fn=collate_fn)
                    val_loader = self.val_loader

                # Train on this PGN file
                self.train_model_with_gui(train_loader, val_loader,
                                        resume_path=self.resume_path,
                                        num_epochs=self.epochs_per_pgn,
                                        save_dir="checkpoints")

                # Mark as processed and update resume path
                self.save_processed_pgn(next_pgn, epochs_trained=self.epochs_per_pgn, file_path=pgn_path)
                self.resume_path = "checkpoints/final.pt"

                # Clear memory
                if self.puzzle_mode.get():
                    del full_dataset, train_subset, val_subset, train_loader, val_loader
                else:
                    del full_dataset, train_loader
                import gc
                gc.collect()

                self.root.after(0, lambda p=next_pgn: self.batch_status_label.config(
                    text=f"✅ Completed {p}. Moving to next file..."))

            self.batch_training_active = False
            self.root.after(0, lambda: messagebox.showinfo("Success", "🎉 Batch training completed!"))

        except Exception as e:
            self.batch_training_active = False
            self.root.after(0, lambda: messagebox.showerror("Error", f"❌ Batch training failed:\n{str(e)}"))

    def train_model_with_gui(self, train_loader, val_loader, resume_path=None, num_epochs=10, save_dir="checkpoints"):
        """Custom training function with GUI callbacks for real-time plotting"""
        import torch
        import torch.nn.functional as F
        from model import ChessNet
        from utils import MOVE_TO_IDX
        import time
        import os

        device = torch.device("cpu")
        model = ChessNet(num_moves=len(MOVE_TO_IDX))
        optimizer = torch.optim.AdamW(model.parameters(), lr=3e-4, weight_decay=1e-4)

        start_epoch = 0
        if resume_path and os.path.exists(resume_path):
            print(f"🔁 Loading checkpoint: {resume_path}")
            checkpoint = torch.load(resume_path, map_location=device)
            model.load_state_dict(checkpoint['model_state_dict'])
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            start_epoch = checkpoint.get('epoch', 0) + 1

        os.makedirs(save_dir, exist_ok=True)
        epoch = start_epoch

        # Calculate target epoch for this training session
        target_epoch = start_epoch + num_epochs
        print(f"Training from epoch {start_epoch + 1} to {target_epoch}")

        for epoch in range(start_epoch, target_epoch):
            print(f"\nEpoch {epoch+1}/{target_epoch}")
            t0 = time.time()

            # Training epoch
            model.train()
            total_policy_loss = 0.0
            total_value_loss = 0.0
            correct = 0
            total = 0

            for x, y_policy, y_value, legality_mask in train_loader:
                x = x.to(device)
                y_policy = y_policy.to(device)
                y_value = y_value.to(device)
                legality_mask = legality_mask.to(device)

                optimizer.zero_grad()
                policy_logits, value_pred = model(x)

                # Apply legality mask
                policy_logits = policy_logits + legality_mask

                policy_loss = F.cross_entropy(policy_logits, y_policy)
                value_loss = F.mse_loss(value_pred.squeeze(), y_value)
                loss = policy_loss + value_loss

                loss.backward()
                optimizer.step()

                total_policy_loss += policy_loss.item()
                total_value_loss += value_loss.item()
                pred = policy_logits.argmax(dim=1)
                correct += pred.eq(y_policy).sum().item()
                total += y_policy.size(0)

            train_policy_loss = total_policy_loss / len(train_loader)
            train_value_loss = total_value_loss / len(train_loader)
            train_acc = 100. * correct / total

            # Validation epoch
            model.eval()
            val_policy_loss = 0.0
            val_value_loss = 0.0
            correct = 0
            total = 0

            with torch.no_grad():
                for x, y_policy, y_value, legality_mask in val_loader:
                    x = x.to(device)
                    y_policy = y_policy.to(device)
                    y_value = y_value.to(device)
                    legality_mask = legality_mask.to(device)

                    policy_logits, value_pred = model(x)
                    policy_logits = policy_logits + legality_mask

                    policy_loss = F.cross_entropy(policy_logits, y_policy)
                    value_loss = F.mse_loss(value_pred.squeeze(), y_value)

                    val_policy_loss += policy_loss.item()
                    val_value_loss += value_loss.item()
                    pred = policy_logits.argmax(dim=1)
                    correct += pred.eq(y_policy).sum().item()
                    total += y_policy.size(0)

            val_policy_loss /= len(val_loader)
            val_value_loss /= len(val_loader)
            val_acc = 100. * correct / total
            t1 = time.time()

            print(f"Train: P={train_policy_loss:.4f}, V={train_value_loss:.4f}, Acc={train_acc:.2f}% | "
                  f"Val: P={val_policy_loss:.4f}, V={val_value_loss:.4f}, Acc={val_acc:.2f}% | Time: {t1-t0:.1f}s")

            # Update GUI plots in main thread
            self.root.after(0, lambda e=epoch+1, tpl=train_policy_loss, tvl=train_value_loss, ta=train_acc,
                           vpl=val_policy_loss, vvl=val_value_loss, va=val_acc:
                           self.update_plots(e, tpl, tvl, ta, vpl, vvl, va))

            # Save checkpoints
            if (epoch + 1) % 2 == 0:
                checkpoint_path = f"{save_dir}/checkpoint_epoch_{epoch+1}.pt"
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                }, checkpoint_path)
                print(f"✅ Checkpoint saved: {checkpoint_path}")

        # Final save
        final_path = f"{save_dir}/final.pt"
        torch.save({
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
        }, final_path)
        print(f"✅ Final checkpoint saved: {final_path}")

        return model

if __name__ == "__main__":
    root = tk.Tk()
    app = ChessTrainerGUI(root)
    root.mainloop()