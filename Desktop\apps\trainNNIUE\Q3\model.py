# model.py
import torch
import torch.nn as nn
import torch.nn.functional as F

class ChessNet(nn.Module):
    def __init__(self, num_moves=1880):
        super().__init__()
        self.num_moves = num_moves

        # CNN backbone
        self.conv1 = nn.Conv2d(14, 64, kernel_size=3, padding=1)
        self.bn1 = nn.BatchNorm2d(64)
        self.conv2 = nn.Conv2d(64, 64, kernel_size=3, padding=1)
        self.bn2 = nn.BatchNorm2d(64)
        self.conv3 = nn.Conv2d(64, 64, kernel_size=3, padding=1)
        self.bn3 = nn.BatchNorm2d(64)

        # Lightweight self-attention over 64 squares
        self.attn_Wq = nn.Linear(64, 16)
        self.attn_Wk = nn.Linear(64, 16)
        self.attn_Wv = nn.Linear(64, 16)
        self.attn_out = nn.Linear(16, 64)

        # Policy head
        self.policy_conv1 = nn.Conv2d(64, 32, kernel_size=1)
        self.policy_conv2 = nn.Conv2d(32, 8, kernel_size=1)
        self.policy_fc = nn.Linear(8 * 64, 64)
        self.policy_out = nn.Linear(64, num_moves)

        # Value head
        self.value_conv = nn.Conv2d(64, 32, kernel_size=1)
        self.value_fc1 = nn.Linear(32, 64)
        self.value_fc2 = nn.Linear(64, 1)

    def forward(self, x):
        h = F.relu(self.bn1(self.conv1(x)))
        h = F.relu(self.bn2(self.conv2(h)))
        h = F.relu(self.bn3(self.conv3(h)))  # (B, 64, 8, 8)

        # Self-attention over spatial positions
        B, C, H, W = h.shape
        h_flat = h.permute(0, 2, 3, 1).reshape(B, H * W, C)  # (B, 64, 64)

        Q = self.attn_Wq(h_flat)
        K = self.attn_Wk(h_flat)
        V = self.attn_Wv(h_flat)
        A = F.softmax(Q.bmm(K.transpose(1, 2)) / 4.0, dim=-1)
        attn_out = self.attn_out(A.bmm(V))
        attn_out = attn_out.reshape(B, H, W, C).permute(0, 3, 1, 2)

        h = h + attn_out  # residual

        # Policy head
        p = F.relu(self.policy_conv1(h))
        p = F.relu(self.policy_conv2(p))
        p = p.reshape(B, -1)
        p = F.relu(self.policy_fc(p))
        policy_logits = self.policy_out(p)

        # Value head
        v = F.relu(self.value_conv(h))
        v = v.mean(dim=[2, 3])
        v = F.relu(self.value_fc1(v))
        value = torch.tanh(self.value_fc2(v))

        return policy_logits, value